<?php

namespace App\Console;

use App\Console\Commands\Admin\AdminInvitationDeleteExpired;
use App\Console\Commands\Afternic\AfternicAudit;
use App\Console\Commands\Client\InviteExpirySystemRefund;
use App\Console\Commands\Domain\DomainRedemptionScheduler;
use App\Console\Commands\RequestDelete\AutoApproveExpiredRequests;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();

        if (config('telescope.enabled')) {
            $schedule->command('telescope:prune')->daily();
        }

        $schedule->command(AdminInvitationDeleteExpired::class)->hourly();
        $schedule->command(AfternicAudit::class)->monthly();
        $schedule->command(InviteExpirySystemRefund::class)->daily();
        $schedule->command(DomainRedemptionScheduler::class)->daily();
        $schedule->command(AutoApproveExpiredRequests::class)->daily();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
