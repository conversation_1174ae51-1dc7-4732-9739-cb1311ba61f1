<?php

namespace App\Console\Commands\RequestDelete;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\RequestDelete\Services\DomainDeleteService;
use Exception;
use Illuminate\Console\Command;

class AutoApproveExpiredRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:auto-approve-expired-requests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically approve domain deletion requests that exceed 24 hours';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'AutoApproveExpiredRequests: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            echo($e->getMessage());
            throw new Exception($errorMsg);
        }
    }

    public function evaluate()
    {
        app(AuthLogger::class)->info('AutoApproveExpiredRequests: Running...');
        
        $processedCount = DomainDeleteService::instance()->processExpiredRequests();
        
        app(AuthLogger::class)->info("AutoApproveExpiredRequests: Processed {$processedCount} expired requests");
        app(AuthLogger::class)->info('AutoApproveExpiredRequests: Done');
    }
}
